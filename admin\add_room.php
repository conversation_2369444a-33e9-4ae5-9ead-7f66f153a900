<?php
require_once '../includes/auth.php';
requireAdminAuth();
require_once '../includes/functions.php';

$success = $error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $persons = (int)$_POST['persons'];
    $type = $_POST['type'] === 'AC' ? 'AC' : 'Non-AC';
    $total_rooms = (int)$_POST['total_rooms'];
    $price = (float)$_POST['price_per_night'];
    $csrf_token = $_POST['csrf_token'] ?? '';

    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid CSRF token.';
    } else {
        $image = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload = uploadImage($_FILES['image'], '../assets/uploads/rooms/');
            if ($upload['success']) {
                $image = $upload['filename'];
            } else {
                $error = $upload['message'];
            }
        }
        if (!$error) {
            global $conn;
            $stmt = $conn->prepare("INSERT INTO rooms (title, description, persons, type, total_rooms, price_per_night, image) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("ssisids", $title, $description, $persons, $type, $total_rooms, $price, $image);
            if ($stmt->execute()) {
                $success = 'Room added successfully!';
            } else {
                $error = 'Database error.';
            }
        }
    }
}
$csrf_token = generateCSRFToken();
include '../includes/header.php';
?>
<?php include '../includes/toast.php'; ?>
<main class="bg-light min-vh-100 pb-5">
  <div class="container py-5">
    <h1 class="h2 fw-bold mb-4">Add Room</h1>
    <?php if ($success): ?><script>showToast('<?php echo $success; ?>', 'success');</script><?php endif; ?>
    <?php if ($error): ?><script>showToast('<?php echo $error; ?>', 'error');</script><?php endif; ?>
    <form method="POST" enctype="multipart/form-data" class="bg-white rounded shadow p-4 needs-validation" autocomplete="off" novalidate>
      <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
      <div class="mb-3">
        <label class="form-label">Room Title</label>
        <input type="text" name="title" required class="form-control" />
        <div class="invalid-feedback">Room title is required.</div>
      </div>
      <div class="mb-3">
        <label class="form-label">Description</label>
        <textarea name="description" required class="form-control"></textarea>
        <div class="invalid-feedback">Description is required.</div>
      </div>
      <div class="mb-3">
        <label class="form-label">Number of Persons</label>
        <input type="number" name="persons" min="1" required class="form-control" />
        <div class="invalid-feedback">Number of persons is required.</div>
      </div>
      <div class="mb-3">
        <label class="form-label">Room Type</label><br>
        <div class="form-check form-check-inline">
          <input class="form-check-input" type="radio" name="type" value="AC" id="typeAC" checked>
          <label class="form-check-label" for="typeAC">AC</label>
        </div>
        <div class="form-check form-check-inline">
          <input class="form-check-input" type="radio" name="type" value="Non-AC" id="typeNonAC">
          <label class="form-check-label" for="typeNonAC">Non-AC</label>
        </div>
      </div>
      <div class="mb-3">
        <label class="form-label">Number of Rooms</label>
        <input type="number" name="total_rooms" min="1" required class="form-control" />
        <div class="invalid-feedback">Number of rooms is required.</div>
      </div>
      <div class="mb-3">
        <label class="form-label">Price per Night</label>
        <input type="number" name="price_per_night" min="0" step="0.01" required class="form-control" />
        <div class="invalid-feedback">Price per night is required.</div>
      </div>
      <div class="mb-3">
        <label class="form-label">Room Image</label>
        <input type="file" name="image" accept="image/jpeg,image/png" class="form-control" />
        <div class="invalid-feedback">Room image is required.</div>
      </div>
      <button type="submit" class="btn btn-primary w-100">Add Room</button>
    </form>
  </div>
</main>
<script>
(function () {
  'use strict';
  var forms = document.querySelectorAll('.needs-validation');
  Array.prototype.slice.call(forms).forEach(function (form) {
    form.addEventListener('submit', function (event) {
      if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
      }
      form.classList.add('was-validated');
    }, false);
  });
})();
</script>
<?php include '../includes/footer.php'; ?>
