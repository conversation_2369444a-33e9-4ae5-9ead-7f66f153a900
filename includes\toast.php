<!-- Usage: include this file and call showToast('Message', 'success'|'error') in JS -->
<div x-data="{ show: false, message: '', type: 'success' }" x-show="show" x-transition class="fixed top-6 right-6 z-50">
  <div :class="type === 'success' ? 'bg-green-500' : 'bg-red-500'" class="text-white px-6 py-3 rounded shadow-lg flex items-center gap-2">
    <span x-text="message"></span>
    <button @click="show = false" class="ml-2 text-lg">&times;</button>
  </div>
  <script>
    window.showToast = function(msg, type = 'success') {
      const toast = document.querySelector('[x-data]');
      if (toast) {
        toast.__x.$data.message = msg;
        toast.__x.$data.type = type;
        toast.__x.$data.show = true;
        setTimeout(() => { toast.__x.$data.show = false; }, 3000);
      }
    }
  </script>
</div>
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script> 