<?php
require_once '../includes/auth.php';
requireAdminAuth();
require_once '../includes/functions.php';

$status = $_GET['status'] ?? '';
$where = '';
if ($status === 'booked') {
    $where = "WHERE status = 'Booked'";
} elseif ($status === 'not_booked') {
    $where = "WHERE status != 'Booked'";
}
$bookings = $conn->query("SELECT b.*, r.title FROM bookings b JOIN rooms r ON b.room_id = r.id $where ORDER BY b.created_at DESC");
include '../includes/header.php';
?>
<?php include '../includes/toast.php'; ?>
<main class="bg-light min-vh-100 pb-5">
  <div class="container py-5">
    <h1 class="h2 fw-bold mb-4">Bookings</h1>
    <div class="mb-4 d-flex gap-2 flex-wrap">
      <a href="?status=booked" class="btn btn-success">Booked</a>
      <a href="?status=not_booked" class="btn btn-warning text-white">Not Booked</a>
      <a href="bookings.php" class="btn btn-primary">All</a>
    </div>
    <div class="table-responsive">
      <table class="table table-bordered table-hover bg-white rounded shadow-sm">
        <thead class="table-light">
          <tr>
            <th>ID</th>
            <th>Room</th>
            <th>Name</th>
            <th>Check-in</th>
            <th>Check-out</th>
            <th>Status</th>
            <th>Details</th>
          </tr>
        </thead>
        <tbody>
          <?php while($row = $bookings->fetch_assoc()): ?>
          <tr>
            <td><?php echo $row['id']; ?></td>
            <td><?php echo htmlspecialchars($row['title']); ?></td>
            <td><?php echo htmlspecialchars($row['name']); ?></td>
            <td><?php echo $row['checkin']; ?></td>
            <td><?php echo $row['checkout']; ?></td>
            <td><?php echo $row['status']; ?></td>
            <td><a href="booking_view.php?id=<?php echo $row['id']; ?>" class="btn btn-link text-primary">View</a></td>
          </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    </div>
  </div>
</main>
<?php include '../includes/footer.php'; ?>
