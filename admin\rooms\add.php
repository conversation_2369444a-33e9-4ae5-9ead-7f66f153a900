<?php
require_once '../../includes/auth.php';
requireAdminAuth();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once '../../includes/functions.php';
    
    $data = [
        'title' => sanitizeInput($_POST['title']),
        'description' => sanitizeInput($_POST['description']),
        'persons' => (int)$_POST['persons'],
        'type' => $_POST['type'],
        'total_rooms' => (int)$_POST['total_rooms'],
        'price' => (float)$_POST['price']
    ];
    
    // Validate and process image upload
    if (isset($_FILES['image'])) {
        $upload = uploadFile($_FILES['image']);
        if ($upload['success']) {
            $data['image'] = $upload['filename'];
        }
    }
    
    // Insert into database
    $stmt = $conn->prepare("INSERT INTO rooms 
        (title, description, persons, type, total_rooms, available_rooms, price_per_night, image) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssisiiis", 
        $data['title'],
        $data['description'],
        $data['persons'],
        $data['type'],
        $data['total_rooms'],
        $data['total_rooms'], // Initially all rooms available
        $data['price'],
        $data['image'] ?? null
    );
    
    if ($stmt->execute()) {
        header("Location: manage.php?success=Room added successfully");
        exit();
    }
}

include '../../includes/header.php';
?>

<!-- Room Add Form with Tailwind -->
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Add New Room</h1>
    
    <form method="POST" enctype="multipart/form-data" class="max-w-lg">
        <!-- Form fields here -->
        <div class="mb-4">
            <label class="block text-gray-700 mb-2">Room Title</label>
            <input type="text" name="title" required class="w-full p-2 border rounded">
        </div>
        <!-- Other fields -->
        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">
            Add Room
        </button>
    </form>
</div>

<?php include '../../includes/footer.php'; ?>
