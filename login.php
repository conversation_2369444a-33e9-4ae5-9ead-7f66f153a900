<?php
require_once 'includes/auth.php';

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    if (adminLogin($username, $password)) {
        header('Location: admin/dashboard.php');
        exit();
    } else {
        $error = 'Invalid credentials';
    }
}
include 'includes/header.php';
?>
<div class="container py-5 min-vh-100 d-flex align-items-center justify-content-center">
  <div class="bg-white rounded shadow p-4 w-100" style="max-width:400px;">
    <h1 class="h3 fw-bold mb-4 text-center">Admin Login</h1>
    <?php if ($error): ?>
      <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="POST" class="needs-validation" novalidate autocomplete="off">
      <div class="mb-3">
        <label class="form-label">Username</label>
        <input type="text" name="username" required class="form-control" />
        <div class="invalid-feedback">Username is required.</div>
      </div>
      <div class="mb-3">
        <label class="form-label">Password</label>
        <input type="password" name="password" required class="form-control" />
        <div class="invalid-feedback">Password is required.</div>
      </div>
      <button type="submit" class="btn btn-primary w-100">Login</button>
    </form>
  </div>
</div>
<script>
(function () {
  'use strict';
  var forms = document.querySelectorAll('.needs-validation');
  Array.prototype.slice.call(forms).forEach(function (form) {
    form.addEventListener('submit', function (event) {
      if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
      }
      form.classList.add('was-validated');
    }, false);
  });
})();
</script>
<?php include 'includes/footer.php'; ?>
