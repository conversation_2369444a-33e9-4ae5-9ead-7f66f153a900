<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Management System</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<?php
if (session_status() === PHP_SESSION_NONE) session_start();
$isAdmin = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$current = basename($_SERVER['SCRIPT_NAME']);
function navActive($file) {
  global $current;
  return $current === $file ? 'active fw-bold text-primary' : '';
}
?>
<nav class="navbar navbar-expand-lg navbar-light bg-white shadow sticky-top">
  <div class="container">
    <a href="<?php echo $isAdmin ? '../index.php' : 'index.php'; ?>" class="navbar-brand fw-bold text-primary">Dolphin Hotel</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar" aria-controls="mainNavbar" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="mainNavbar">
      <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
        <?php if ($isAdmin): ?>
          <li class="nav-item"><a href="dashboard.php" class="nav-link <?php echo navActive('dashboard.php'); ?>" aria-current="<?php echo $current === 'dashboard.php' ? 'page' : false; ?>">Dashboard</a></li>
          <li class="nav-item"><a href="add_room.php" class="nav-link <?php echo navActive('add_room.php'); ?>" aria-current="<?php echo $current === 'add_room.php' ? 'page' : false; ?>">Add Room</a></li>
          <li class="nav-item"><a href="bookings.php" class="nav-link <?php echo navActive('bookings.php'); ?>" aria-current="<?php echo $current === 'bookings.php' ? 'page' : false; ?>">Bookings</a></li>
          <li class="nav-item"><a href="sales_report.php" class="nav-link <?php echo navActive('sales_report.php'); ?>" aria-current="<?php echo $current === 'sales_report.php' ? 'page' : false; ?>">Sales</a></li>
          <li class="nav-item"><a href="../logout.php" class="btn btn-danger ms-2">Logout</a></li>
        <?php else: ?>
          <li class="nav-item"><a href="index.php" class="nav-link <?php echo navActive('index.php'); ?>" aria-current="<?php echo $current === 'index.php' ? 'page' : false; ?>">Home</a></li>
          <li class="nav-item"><a href="booking.php" class="nav-link <?php echo navActive('booking.php'); ?>" aria-current="<?php echo $current === 'booking.php' ? 'page' : false; ?>">Book a Room</a></li>
          <li class="nav-item"><a href="#contact" class="nav-link">Contact</a></li>
          <li class="nav-item"><a href="login.php" class="btn btn-primary ms-2 <?php echo navActive('login.php'); ?>" aria-current="<?php echo $current === 'login.php' ? 'page' : false; ?>">Admin Login</a></li>
        <?php endif; ?>
      </ul>
    </div>
  </div>
</nav>
<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
