<?php
require_once '../includes/auth.php';
requireAdminAuth();
require_once '../includes/functions.php';

$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$booking = $conn->query("SELECT b.*, r.title AS room_title FROM bookings b JOIN rooms r ON b.room_id = r.id WHERE b.id = $id")->fetch_assoc();

include '../includes/header.php';
?>
<main class="bg-light min-vh-100 pb-5">
  <div class="container py-5">
    <a href="bookings.php" class="text-primary text-decoration-none mb-4 d-inline-block">&larr; Back to Bookings</a>
    <h1 class="h2 fw-bold mb-4 d-flex align-items-center gap-2"><i class="bi bi-card-list"></i> Booking Details</h1>
    <?php if (!$booking): ?>
      <div class="alert alert-danger">Booking not found.</div>
    <?php else: ?>
      <div class="card shadow-lg border-primary mb-4">
        <div class="card-body">
          <div class="mb-3">
            <span class="badge bg-primary">Room</span>
            <span class="fw-bold ms-2 text-primary"><?php echo htmlspecialchars($booking['room_title']); ?></span>
          </div>
          <div class="row g-4">
            <div class="col-md-7">
              <div class="mb-2"><span class="fw-semibold">Guest Name:</span> <?php echo htmlspecialchars($booking['name']); ?></div>
              <div class="mb-2"><span class="fw-semibold">Phone:</span> <?php echo htmlspecialchars($booking['phone']); ?></div>
              <div class="mb-2"><span class="fw-semibold">Aadhar Number:</span> <?php echo htmlspecialchars($booking['aadhar_number']); ?></div>
              <div class="mb-2"><span class="fw-semibold">Check-in:</span> <span class="badge bg-success ms-1"><?php echo htmlspecialchars($booking['checkin']); ?></span></div>
              <div class="mb-2"><span class="fw-semibold">Check-out:</span> <span class="badge bg-warning text-dark ms-1"><?php echo htmlspecialchars($booking['checkout']); ?></span></div>
              <div class="mb-2"><span class="fw-semibold">Payment:</span> <?php echo htmlspecialchars($booking['payment_method']); ?></div>
              <div class="mb-2"><span class="fw-semibold">Status:</span> <span class="badge <?php echo $booking['status'] === 'Booked' ? 'bg-primary' : ($booking['status'] === 'Completed' ? 'bg-success' : 'bg-danger'); ?> ms-1"><?php echo htmlspecialchars($booking['status']); ?></span></div>
            </div>
            <div class="col-md-5 text-center">
              <span class="fw-semibold mb-1 d-block">Aadhar Image:</span>
              <?php if ($booking['aadhar_image']): ?>
                <img src="../assets/uploads/aadhar/<?php echo htmlspecialchars($booking['aadhar_image']); ?>" alt="Aadhar" class="img-thumbnail rounded shadow" style="height:120px;width:120px;object-fit:cover;">
              <?php else: ?>
                <span class="text-muted">No image</span>
              <?php endif; ?>
            </div>
          </div>
          <div class="mt-3"><span class="fw-semibold">Address:</span> <?php echo htmlspecialchars($booking['address']); ?></div>
          <div class="mt-2"><span class="fw-semibold">Total Price:</span> <span class="fs-5 text-success fw-bold">₹<?php echo number_format($booking['total_price'],2); ?></span></div>
          <div class="mt-2 text-muted small">Created At: <?php echo htmlspecialchars($booking['created_at']); ?></div>
        </div>
      </div>
    <?php endif; ?>
  </div>
</main>
<?php include '../includes/footer.php'; ?> 