<?php
require_once '../includes/auth.php';
requireAdminAuth();
require_once '../includes/functions.php';

// Daily sales
$daily = $conn->query("SELECT DATE(created_at) as day, SUM(total_price) as total FROM bookings WHERE status = 'Booked' GROUP BY day ORDER BY day DESC LIMIT 30");
// Monthly sales
$monthly = $conn->query("SELECT DATE_FORMAT(created_at, '%Y-%m') as month, SUM(total_price) as total FROM bookings WHERE status = 'Booked' GROUP BY month ORDER BY month DESC LIMIT 12");
include '../includes/header.php';
?>
<?php include '../includes/toast.php'; ?>
<main class="bg-light min-vh-100 pb-5">
  <div class="container py-5">
    <h1 class="h2 fw-bold mb-4">Sales Report</h1>
    <h2 class="h5 fw-semibold mt-4 mb-2">Daily Sales</h2>
    <div class="table-responsive mb-5">
      <table class="table table-bordered table-hover bg-white rounded shadow-sm">
        <thead class="table-light">
          <tr>
            <th>Date</th>
            <th>Total Sales</th>
          </tr>
        </thead>
        <tbody>
          <?php while($row = $daily->fetch_assoc()): ?>
          <tr>
            <td><?php echo $row['day']; ?></td>
            <td>₹<?php echo number_format($row['total'],2); ?></td>
          </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    </div>
    <h2 class="h5 fw-semibold mt-4 mb-2">Monthly Sales</h2>
    <div class="table-responsive">
      <table class="table table-bordered table-hover bg-white rounded shadow-sm">
        <thead class="table-light">
          <tr>
            <th>Month</th>
            <th>Total Sales</th>
          </tr>
        </thead>
        <tbody>
          <?php while($row = $monthly->fetch_assoc()): ?>
          <tr>
            <td><?php echo $row['month']; ?></td>
            <td>₹<?php echo number_format($row['total'],2); ?></td>
          </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    </div>
  </div>
</main>
<?php include '../includes/footer.php'; ?>
