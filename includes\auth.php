<?php
require_once 'config.php';

function adminLogin($username, $password) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT id, password_hash FROM admin_users WHERE username = ?");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 1) {
        $admin = $result->fetch_assoc();
        if (password_verify($password, $admin['password_hash'])) {
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_logged_in'] = true;
            return true;
        }
    }
    return false;
}

function isAdminLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

function adminLogout() {
    session_unset();
    session_destroy();
}

function requireAdminAuth() {
    if (!isAdminLoggedIn()) {
        header("Location: ../login.php");
        exit();
    }
}
?>
