<?php
require_once '../includes/auth.php';
requireAdminAuth();

$total_rooms = $conn->query("SELECT COUNT(*) FROM rooms")->fetch_row()[0];
$booked_rooms = $conn->query("SELECT COUNT(DISTINCT room_id) FROM bookings WHERE status = 'Booked'")->fetch_row()[0];
$available_rooms = $total_rooms - $booked_rooms;
$today_earnings = $conn->query("SELECT SUM(total_price) FROM bookings WHERE status = 'Booked' AND DATE(created_at) = CURDATE()")->fetch_row()[0] ?? 0;

include '../includes/header.php';
?>
<main class="bg-light min-vh-100 pb-5">
  <div class="container py-5">
    <h1 class="h2 fw-bold mb-4">Admin Dashboard</h1>
    <div class="row g-4 mb-4">
      <div class="col-md-3">
        <div class="card text-center shadow-sm">
          <div class="card-body">
            <div class="display-6 fw-bold text-primary mb-2"><?php echo $total_rooms; ?></div>
            <div class="text-muted">Total Rooms</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card text-center shadow-sm">
          <div class="card-body">
            <div class="display-6 fw-bold text-success mb-2"><?php echo $booked_rooms; ?></div>
            <div class="text-muted">Booked Rooms</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card text-center shadow-sm">
          <div class="card-body">
            <div class="display-6 fw-bold text-warning mb-2"><?php echo $available_rooms; ?></div>
            <div class="text-muted">Available Rooms</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card text-center shadow-sm">
          <div class="card-body">
            <div class="display-6 fw-bold text-info mb-2">₹<?php echo number_format($today_earnings,2); ?></div>
            <div class="text-muted">Today's Earnings</div>
          </div>
        </div>
      </div>
    </div>
    <div class="d-flex flex-wrap gap-3 mb-4">
      <a href="add_room.php" class="btn btn-primary">➕ Add Room</a>
      <a href="bookings.php" class="btn btn-success">📋 Manage Bookings</a>
      <a href="sales_report.php" class="btn btn-info text-white">📊 View Sales Report</a>
    </div>
  </div>
</main>
<?php include '../includes/footer.php'; ?>
