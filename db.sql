-- Database: hotel_management
CREATE DATABASE hotel_management;
USE hotel_management;

-- Rooms table
DROP TABLE IF EXISTS rooms;
CREATE TABLE rooms (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  description TEXT,
  persons INT,
  type ENUM('AC','Non-AC'),
  total_rooms INT,
  price_per_night DECIMAL(10,2),
  image VARCHAR(255)
);

-- Bookings table
DROP TABLE IF EXISTS bookings;
CREATE TABLE bookings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  room_id INT,
  name VARCHAR(100),
  phone VARCHAR(15),
  aadhar_number VARCHAR(20),
  aadhar_image VARCHAR(255),
  address TEXT,
  checkin DATE,
  checkout DATE,
  payment_method VARCHAR(50),
  total_price DECIMAL(10,2),
  status ENUM('Booked', 'Cancelled', 'Completed') DEFAULT 'Booked',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  <PERSON>OR<PERSON><PERSON><PERSON> KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE
);

-- Admin users table
CREATE TABLE admin_users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(100) NOT NULL,
  last_login TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payments table (for future scalability)
CREATE TABLE payments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  booking_id INT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  payment_method VARCHAR(50) NOT NULL,
  transaction_id VARCHAR(100),
  status ENUM('Pending', 'Completed', 'Failed') DEFAULT 'Pending',
  payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (booking_id) REFERENCES bookings(id)
);