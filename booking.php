<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Handle booking form submission
$success = $error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid CSRF token.';
    } else {
        $room_id = (int)$_POST['room_id'];
        $aadhar = sanitizeInput($_POST['aadhar']);
        $name = sanitizeInput($_POST['name']);
        $phone = sanitizeInput($_POST['phone']);
        $address = sanitizeInput($_POST['address']);
        $checkin = $_POST['checkin'];
        $checkout = $_POST['checkout'];
        $payment = sanitizeInput($_POST['payment_method']);
        $aadhar_img = '';
        if (isset($_FILES['aadhar_img']) && $_FILES['aadhar_img']['error'] === UPLOAD_ERR_OK) {
            $upload = uploadImage($_FILES['aadhar_img'], 'assets/uploads/aadhar/');
            if ($upload['success']) {
                $aadhar_img = $upload['filename'];
            } else {
                $error = $upload['message'];
            }
        } else {
            $error = 'Aadhar image required.';
        }
        if (!$error) {
            // Calculate price
            $room = $conn->query("SELECT price_per_night FROM rooms WHERE id = $room_id")->fetch_assoc();
            $days = (strtotime($checkout) - strtotime($checkin)) / (60*60*24);
            $days = max(1, $days);
            $total_price = $room ? $room['price_per_night'] * $days : 0;
            $stmt = $conn->prepare("INSERT INTO bookings (room_id, name, phone, aadhar_number, aadhar_image, address, checkin, checkout, payment_method, total_price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("issssssssd", $room_id, $name, $phone, $aadhar, $aadhar_img, $address, $checkin, $checkout, $payment, $total_price);
            if ($stmt->execute()) {
                $success = 'Booking successful!';
            } else {
                $error = 'Database error.';
            }
        }
    }
}
$csrf_token = generateCSRFToken();
include 'includes/header.php';
?>
<?php include 'includes/toast.php'; ?>
<?php
$rooms = $conn->query("SELECT * FROM rooms WHERE id NOT IN (SELECT room_id FROM bookings WHERE status = 'Booked')");
?>
<main class="bg-light min-vh-100 pb-5">
  <div class="container py-5">
    <h2 class="h2 fw-bold mb-4 text-center">Available Rooms</h2>
    <?php if ($success): ?>
      <script>showToast('<?php echo $success; ?>', 'success');</script>
    <?php elseif ($error): ?>
      <script>showToast('<?php echo $error; ?>', 'error');</script>
    <?php endif; ?>
    <div class="row g-4">
      <?php while($room = $rooms->fetch_assoc()): ?>
      <div class="col-md-6">
        <div class="card h-100 shadow-sm">
          <img src="assets/uploads/rooms/<?php echo htmlspecialchars($room['image']); ?>" alt="Room Image" class="card-img-top" style="height:220px;object-fit:cover;">
          <div class="card-body">
            <h5 class="card-title mb-2"><?php echo htmlspecialchars($room['title']); ?></h5>
            <p class="card-text mb-1"><?php echo htmlspecialchars($room['description']); ?></p>
            <div class="mb-2 small text-muted">Type: <span class="fw-semibold"><?php echo htmlspecialchars($room['type']); ?></span> | Persons: <span class="fw-semibold"><?php echo (int)$room['persons']; ?></span></div>
            <div class="mb-2">Price per night: <span class="badge bg-primary">₹<?php echo number_format($room['price_per_night'],2); ?></span></div>
            <button type="button" class="btn btn-success w-100" data-bs-toggle="modal" data-bs-target="#bookingModal" onclick="openModal('<?php echo $room['id']; ?>', '<?php echo $room['price_per_night']; ?>')">Book Now</button>
          </div>
        </div>
      </div>
      <?php endwhile; ?>
    </div>
  </div>
  <!-- Booking Modal -->
  <div class="modal fade" id="bookingModal" tabindex="-1" aria-labelledby="bookingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="bookingModalLabel">Book Room (<span class="text-primary">₹<span id="room_price"></span>/night</span>)</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form method="post" enctype="multipart/form-data" class="needs-validation" id="bookingForm" autocomplete="off" novalidate onsubmit="return validateBookingForm()">
          <div class="modal-body">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
            <input type="hidden" name="room_id" id="room_id">
            <div class="mb-3">
              <label class="form-label">Aadhar No</label>
              <input type="text" name="aadhar" required maxlength="20" class="form-control" />
              <div class="invalid-feedback">Aadhar number is required.</div>
            </div>
            <div class="mb-3">
              <label class="form-label">Aadhar Image (JPG/PNG, max 2MB)</label>
              <input type="file" name="aadhar_img" accept="image/jpeg,image/png" required class="form-control" />
              <div class="invalid-feedback">Aadhar image is required.</div>
            </div>
            <div class="mb-3">
              <label class="form-label">Name</label>
              <input type="text" name="name" required maxlength="100" class="form-control" />
              <div class="invalid-feedback">Name is required.</div>
            </div>
            <div class="mb-3">
              <label class="form-label">Phone</label>
              <input type="tel" name="phone" required maxlength="15" pattern="[0-9]{10,15}" placeholder="10-15 digit number" class="form-control" />
              <div class="invalid-feedback">Valid phone number is required.</div>
            </div>
            <div class="mb-3">
              <label class="form-label">Address</label>
              <textarea name="address" required maxlength="255" class="form-control"></textarea>
              <div class="invalid-feedback">Address is required.</div>
            </div>
            <div class="row">
              <div class="col">
                <label class="form-label">Check-in</label>
                <input type="date" name="checkin" id="checkin" required class="form-control" />
                <div class="invalid-feedback">Check-in date is required.</div>
              </div>
              <div class="col">
                <label class="form-label">Check-out</label>
                <input type="date" name="checkout" id="checkout" required class="form-control" />
                <div class="invalid-feedback">Check-out date is required.</div>
              </div>
            </div>
            <div class="mb-3 mt-3">
              <label class="form-label">Payment</label>
              <select name="payment_method" required class="form-select">
                <option value="UPI">UPI</option>
                <option value="Cash">Cash</option>
                <option value="Card">Card</option>
              </select>
              <div class="invalid-feedback">Payment method is required.</div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-success w-100">Confirm Booking</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</main>
<script>
function openModal(roomId, price) {
  document.getElementById('room_id').value = roomId;
  document.getElementById('room_price').innerText = price;
}
// Bootstrap validation
(function () {
  'use strict';
  var forms = document.querySelectorAll('.needs-validation');
  Array.prototype.slice.call(forms).forEach(function (form) {
    form.addEventListener('submit', function (event) {
      if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
      }
      form.classList.add('was-validated');
    }, false);
  });
})();
function validateBookingForm() {
  const phone = document.querySelector('input[name="phone"]').value;
  const checkin = document.getElementById('checkin').value;
  const checkout = document.getElementById('checkout').value;
  if (!/^\d{10,15}$/.test(phone)) {
    showToast('Please enter a valid phone number (10-15 digits).', 'error');
    return false;
  }
  if (checkin === '' || checkout === '' || new Date(checkout) <= new Date(checkin)) {
    showToast('Check-out date must be after check-in date.', 'error');
    return false;
  }
  return true;
}
</script>
<?php include 'includes/footer.php'; ?>
