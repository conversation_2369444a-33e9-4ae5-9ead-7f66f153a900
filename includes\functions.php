<?php
// Utility functions for hotel management system

function sanitizeInput($input) {
    global $conn;
    return htmlspecialchars(mysqli_real_escape_string($conn, trim($input)), ENT_QUOTES, 'UTF-8');
}

function uploadFile($file) {
    $targetDir = __DIR__ . '/../assets/uploads/';
    $filename = uniqid() . '_' . basename($file['name']);
    $targetFile = $targetDir . $filename;
    $allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'File too large'];
    }
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type'];
    }
    if (move_uploaded_file($file['tmp_name'], $targetFile)) {
        return ['success' => true, 'filename' => $filename];
    } else {
        return ['success' => false, 'message' => 'Upload failed'];
    }
}

function uploadImage($file, $targetDir) {
    $allowedTypes = ['image/jpeg', 'image/png'];
    $maxSize = 2 * 1024 * 1024; // 2MB
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'File too large (max 2MB)'];
    }
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type'];
    }
    $ext = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid('img_', true) . '.' . $ext;
    $target = rtrim($targetDir, '/') . '/' . $filename;
    if (move_uploaded_file($file['tmp_name'], $target)) {
        return ['success' => true, 'filename' => $filename];
    }
    return ['success' => false, 'message' => 'Upload failed'];
}

function calculateBookingPrice($room_id, $checkin, $checkout) {
    global $conn;
    $stmt = $conn->prepare("SELECT price_per_night FROM rooms WHERE id = ?");
    $stmt->bind_param("i", $room_id);
    $stmt->execute();
    $stmt->bind_result($price);
    $stmt->fetch();
    $stmt->close();
    $days = (strtotime($checkout) - strtotime($checkin)) / (60 * 60 * 24);
    return $price * $days;
}

// CSRF token helpers
function generateCSRFToken() {
    if (empty($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}
